// // App button widget will be defined here
// import 'package:flutter/material.dart';
// import '../theme/app_colors.dart';
// import '../theme/app_typography.dart';

// class AppButton extends StatelessWidget {
//   final String label;
//   final VoidCallback? onPressed;
//   final bool isEnabled;

//   // Customizable properties
//   final double? width;
//   final double height;
//   final double borderRadius;
//   final EdgeInsetsGeometry? margin;
//   final EdgeInsetsGeometry padding;
//   final Gradient? gradient;
//   final BoxDecoration? decoration;
//   final TextStyle? textStyle;

//   const AppButton({
//     super.key,
//     required this.label,
//     required this.onPressed,
//     this.isEnabled = true,
//     this.width,
//     this.height = 50,
//     this.borderRadius = 10,
//     this.margin,
//     this.padding = const EdgeInsets.symmetric(horizontal: 16),
//     this.gradient,
//     this.decoration,
//     this.textStyle,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       margin: margin,
//       width: width ?? double.infinity,
//       height: height,
//       decoration:
//           decoration ??
//           BoxDecoration(
//             borderRadius: BorderRadius.circular(borderRadius),
//             gradient: isEnabled
//                 ? (gradient ??
//                       const LinearGradient(
//                         colors: [
//                           AppColors.secondary,
//                           Color.fromARGB(255, 226, 94, 6),
//                         ],
//                         begin: Alignment.centerLeft,
//                         end: Alignment.centerRight,
//                       ))
//                 : null,
//             color: !isEnabled ? AppColors.disabled : null,
//           ),
//       child: ElevatedButton(
//         style: ElevatedButton.styleFrom(
//           backgroundColor: const Color.fromARGB(0, 108, 107, 107),
// shadowColor: const Color.fromARGB(33, 250, 248, 248),
//           shape: RoundedRectangleBorder(
//             borderRadius: BorderRadius.circular(borderRadius),
//           ),
//         ),
//         onPressed: isEnabled ? onPressed : null,
//         child: Padding(
//           padding: padding,
//           child: Text(label, style: textStyle ?? AppTypography.button),
//         ),
//       ),
//     );
//   }
// }

import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import '../theme/app_typography.dart';

/// Enhanced button widget supporting different styles from the screen designs
class AppButton extends StatelessWidget {
  final String label;
  final VoidCallback? onPressed;
  final bool isEnabled;
  final AppButtonType type;
  final Widget? icon;
  final bool isLoading;

  // Customizable properties
  final double? width;
  final double height;
  final double borderRadius;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry padding;
  final TextStyle? textStyle;

  const AppButton({
    super.key,
    required this.label,
    required this.onPressed,
    this.isEnabled = true,
    this.type = AppButtonType.primary,
    this.icon,
    this.isLoading = false,
    this.width,
    this.height = 48,
    this.borderRadius = 12,
    this.margin,
    this.padding = const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    this.textStyle,
  });

  /// Primary gradient button (orange gradient)
  const AppButton.primary({
    super.key,
    required this.label,
    required this.onPressed,
    this.isEnabled = true,
    this.icon,
    this.isLoading = false,
    this.width,
    this.height = 48,
    this.borderRadius = 12,
    this.margin,
    this.padding = const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    this.textStyle,
  }) : type = AppButtonType.primary;

  /// Secondary outline button
  const AppButton.secondary({
    super.key,
    required this.label,
    required this.onPressed,
    this.isEnabled = true,
    this.icon,
    this.isLoading = false,
    this.width,
    this.height = 48,
    this.borderRadius = 12,
    this.margin,
    this.padding = const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    this.textStyle,
  }) : type = AppButtonType.secondary;

  /// Text button
  const AppButton.text({
    super.key,
    required this.label,
    required this.onPressed,
    this.isEnabled = true,
    this.icon,
    this.isLoading = false,
    this.width,
    this.height = 40,
    this.borderRadius = 8,
    this.margin,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    this.textStyle,
  }) : type = AppButtonType.text;

  /// Icon button
  const AppButton.icon({
    super.key,
    required this.label,
    required this.onPressed,
    required this.icon,
    this.isEnabled = true,
    this.isLoading = false,
    this.width,
    this.height = 48,
    this.borderRadius = 12,
    this.margin,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    this.textStyle,
  }) : type = AppButtonType.icon;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      width: width,
      height: height,
      child: _buildButton(),
    );
  }

  Widget _buildButton() {
    switch (type) {
      case AppButtonType.primary:
        return _buildPrimaryButton();
      case AppButtonType.secondary:
        return _buildSecondaryButton();
      case AppButtonType.text:
        return _buildTextButton();
      case AppButtonType.icon:
        return _buildIconButton();
    }
  }

  Widget _buildPrimaryButton() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        gradient: isEnabled
            ? const LinearGradient(
                colors: AppColors.secondaryGradient,
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              )
            : null,
        color: !isEnabled ? AppColors.disabled : null,
      ),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          padding: padding,
        ),
        onPressed: isEnabled && !isLoading ? onPressed : null,
        child: _buildButtonContent(),
      ),
    );
  }

  Widget _buildSecondaryButton() {
    return OutlinedButton(
      style: OutlinedButton.styleFrom(
        foregroundColor: isEnabled ? AppColors.primary : AppColors.disabled,
        side: BorderSide(
          color: isEnabled ? AppColors.primary : AppColors.disabled,
          width: 1.5,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        padding: padding,
      ),
      onPressed: isEnabled && !isLoading ? onPressed : null,
      child: _buildButtonContent(),
    );
  }

  Widget _buildTextButton() {
    return TextButton(
      style: TextButton.styleFrom(
        foregroundColor: isEnabled ? AppColors.primary : AppColors.disabled,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        padding: padding,
      ),
      onPressed: isEnabled && !isLoading ? onPressed : null,
      child: _buildButtonContent(),
    );
  }

  Widget _buildIconButton() {
    return ElevatedButton.icon(
      style: ElevatedButton.styleFrom(
        backgroundColor: isEnabled ? AppColors.secondary : AppColors.disabled,
        foregroundColor: AppColors.textOnSecondary,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        padding: padding,
      ),
      onPressed: isEnabled && !isLoading ? onPressed : null,
      icon: icon ?? const SizedBox.shrink(),
      label: _buildButtonContent(),
    );
  }

  Widget _buildButtonContent() {
    if (isLoading) {
      return SizedBox(
        height: 20,
        width: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            type == AppButtonType.primary || type == AppButtonType.icon
                ? AppColors.textOnSecondary
                : AppColors.primary,
          ),
        ),
      );
    }

    final textColor = _getTextColor();
    final style =
        textStyle ?? _getDefaultTextStyle().copyWith(color: textColor);

    if (icon != null && type != AppButtonType.icon) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          icon!,
          const SizedBox(width: 8),
          Text(label, style: style),
        ],
      );
    }

    return Text(label, style: style);
  }

  Color _getTextColor() {
    if (!isEnabled) return AppColors.disabled;

    switch (type) {
      case AppButtonType.primary:
      case AppButtonType.icon:
        return AppColors.textOnSecondary;
      case AppButtonType.secondary:
      case AppButtonType.text:
        return AppColors.primary;
    }
  }

  TextStyle _getDefaultTextStyle() {
    switch (type) {
      case AppButtonType.primary:
      case AppButtonType.icon:
        return AppTypography.buttonPrimary;
      case AppButtonType.secondary:
        return AppTypography.buttonSecondary;
      case AppButtonType.text:
        return AppTypography.buttonText;
    }
  }
}

/// Different button types for various use cases
enum AppButtonType { primary, secondary, text, icon }
