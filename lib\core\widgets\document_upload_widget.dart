import 'dart:io';
import 'package:flutter/material.dart';
// import 'package:image_picker/image_picker.dart';
import '../theme/app_colors.dart';
import '../theme/app_typography.dart';
import 'app_card.dart';

/// Document upload widget for KYC and other document uploads
class DocumentUploadWidget extends StatelessWidget {
  final String title;
  final String subtitle;
  final File? uploadedFile;
  final VoidCallback onTap;
  final bool isRequired;
  final bool isUploaded;
  final IconData? icon;

  const DocumentUploadWidget({
    super.key,
    required this.title,
    required this.subtitle,
    required this.onTap,
    this.uploadedFile,
    this.isRequired = false,
    this.isUploaded = false,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return AppCard.upload(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Upload Icon/Status
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: isUploaded
                      ? AppColors.success
                      : AppColors.surfaceVariant,
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color: isUploaded ? AppColors.success : AppColors.border,
                    width: 2,
                  ),
                ),
                child: Icon(
                  isUploaded ? Icons.check_circle : (icon ?? Icons.upload_file),
                  size: 24,
                  color: isUploaded ? Colors.white : AppColors.textSecondary,
                ),
              ),

              const SizedBox(width: 16),

              // Document Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            title,
                            style: AppTypography.subtitle1.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        if (isRequired)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.error,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'Required',
                              style: AppTypography.caption.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: AppTypography.body2.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    if (isUploaded && uploadedFile != null) ...[
                      const SizedBox(height: 8),
                      Text(
                        'File uploaded successfully',
                        style: AppTypography.caption.copyWith(
                          color: AppColors.success,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ],
                ),
              ),

              // Upload Arrow
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: AppColors.textTertiary,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Profile photo upload widget
class ProfilePhotoUploadWidget extends StatelessWidget {
  final File? profileImage;
  final VoidCallback onTap;
  final double size;
  final String uploadText;

  const ProfilePhotoUploadWidget({
    super.key,
    required this.onTap,
    this.profileImage,
    this.size = 100,
    this.uploadText = 'Upload Photo',
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: AppColors.surfaceVariant,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppColors.border,
            width: 2,
            style: BorderStyle.solid,
          ),
        ),
        child: profileImage != null
            ? ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: Image.file(
                  profileImage!,
                  width: size,
                  height: size,
                  fit: BoxFit.cover,
                ),
              )
            : Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.camera_alt,
                    size: size * 0.3,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    uploadText,
                    style: AppTypography.caption.copyWith(
                      color: AppColors.textSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
      ),
    );
  }
}

/// Section header widget
class SectionHeaderWidget extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Widget? trailing;
  final EdgeInsetsGeometry? padding;

  const SectionHeaderWidget({
    super.key,
    required this.title,
    this.subtitle,
    this.trailing,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: AppTypography.sectionHeader),
                if (subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(subtitle!, style: AppTypography.sectionSubheader),
                ],
              ],
            ),
          ),
          if (trailing != null) trailing!,
        ],
      ),
    );
  }
}

/// Status indicator widget
class StatusIndicatorWidget extends StatelessWidget {
  final String label;
  final bool isCompleted;
  final bool isActive;
  final IconData? icon;

  const StatusIndicatorWidget({
    super.key,
    required this.label,
    this.isCompleted = false,
    this.isActive = false,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          // Status Icon
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: isCompleted
                  ? AppColors.success
                  : (isActive ? AppColors.primary : AppColors.disabled),
              shape: BoxShape.circle,
            ),
            child: Icon(
              isCompleted ? Icons.check : (icon ?? Icons.circle),
              size: 16,
              color: Colors.white,
            ),
          ),

          const SizedBox(width: 12),

          // Status Label
          Expanded(
            child: Text(
              label,
              style: AppTypography.body1.copyWith(
                color: isCompleted || isActive
                    ? AppColors.textPrimary
                    : AppColors.textSecondary,
                fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
              ),
            ),
          ),

          // Status Indicator
          if (isCompleted)
            Icon(Icons.check_circle, size: 20, color: AppColors.success)
          else if (isActive)
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            ),
        ],
      ),
    );
  }
}
