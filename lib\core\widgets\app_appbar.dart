import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/app_colors.dart';
import '../theme/app_typography.dart';

/// Enhanced app bar widget matching the blue gradient design from the screens
class AppAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final bool centerTitle;
  final bool showLogo;
  final double elevation;
  final Color? backgroundColor;
  final SystemUiOverlayStyle? systemOverlayStyle;

  const AppAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.centerTitle = true,
    this.showLogo = false,
    this.elevation = 0,
    this.backgroundColor,
    this.systemOverlayStyle,
  });

  /// App bar with logo (for main screens)
  const AppAppBar.withLogo({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.centerTitle = true,
    this.elevation = 0,
    this.backgroundColor,
    this.systemOverlayStyle,
  }) : showLogo = true;

  /// Simple app bar without logo (for secondary screens)
  const AppAppBar.simple({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.centerTitle = true,
    this.elevation = 0,
    this.backgroundColor,
    this.systemOverlayStyle,
  }) : showLogo = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: AppColors.primaryGradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: AppBar(
        title: _buildTitle(),
        actions: actions,
        leading: leading,
        automaticallyImplyLeading: automaticallyImplyLeading,
        centerTitle: centerTitle,
        elevation: elevation,
        backgroundColor: backgroundColor ?? Colors.transparent,
        foregroundColor: AppColors.textOnPrimary,
        systemOverlayStyle:
            systemOverlayStyle ??
            const SystemUiOverlayStyle(
              statusBarColor: Colors.transparent,
              statusBarIconBrightness: Brightness.light,
              statusBarBrightness: Brightness.dark,
            ),
        iconTheme: const IconThemeData(
          color: AppColors.textOnPrimary,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildTitle() {
    if (showLogo) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Logo placeholder - replace with actual logo
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.local_shipping,
              color: AppColors.primary,
              size: 20,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: AppTypography.appBarTitle.copyWith(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      );
    }

    return Text(title, style: AppTypography.appBarTitle);
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

/// Gradient app bar for special screens (like registration flow)
class GradientAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final String? subtitle;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final bool centerTitle;
  final double elevation;
  final List<Color>? gradientColors;

  const GradientAppBar({
    super.key,
    required this.title,
    this.subtitle,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.centerTitle = true,
    this.elevation = 0,
    this.gradientColors,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: gradientColors ?? AppColors.primaryGradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: AppBar(
        title: _buildTitle(),
        actions: actions,
        leading: leading,
        automaticallyImplyLeading: automaticallyImplyLeading,
        centerTitle: centerTitle,
        elevation: elevation,
        backgroundColor: Colors.transparent,
        foregroundColor: AppColors.textOnPrimary,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.dark,
        ),
        iconTheme: const IconThemeData(
          color: AppColors.textOnPrimary,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildTitle() {
    if (subtitle != null) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(title, style: AppTypography.screenTitle),
          const SizedBox(height: 2),
          Text(
            subtitle!,
            style: AppTypography.body2.copyWith(
              color: AppColors.textOnPrimary.withOpacity(0.8),
            ),
          ),
        ],
      );
    }

    return Text(title, style: AppTypography.screenTitle);
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
