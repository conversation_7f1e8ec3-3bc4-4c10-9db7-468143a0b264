import 'package:flutter/material.dart';
import '../theme/app_colors.dart';

/// Enhanced card widget supporting different styles from the screen designs
class AppCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;
  final double borderRadius;
  final Color? backgroundColor;
  final BoxBorder? border;
  final List<BoxShadow>? boxShadow;
  final double? width;
  final double? height;
  final AppCardType type;

  const AppCard({
    super.key,
    required this.child,
    this.padding = const EdgeInsets.all(20),
    this.margin = const EdgeInsets.only(bottom: 16),
    this.borderRadius = 12,
    this.backgroundColor,
    this.border,
    this.boxShadow,
    this.width,
    this.height,
    this.type = AppCardType.standard,
  });

  /// Standard card with white background and subtle shadow
  const AppCard.standard({
    super.key,
    required this.child,
    this.padding = const EdgeInsets.all(20),
    this.margin = const EdgeInsets.only(bottom: 16),
    this.borderRadius = 12,
    this.width,
    this.height,
  }) : backgroundColor = null,
       border = null,
       boxShadow = null,
       type = AppCardType.standard;

  /// Section card for grouping related content
  const AppCard.section({
    super.key,
    required this.child,
    this.padding = const EdgeInsets.all(24),
    this.margin = const EdgeInsets.only(bottom: 24),
    this.borderRadius = 16,
    this.width,
    this.height,
  }) : backgroundColor = null,
       border = null,
       boxShadow = null,
       type = AppCardType.section;

  /// Document upload card with dashed border
  const AppCard.upload({
    super.key,
    required this.child,
    this.padding = const EdgeInsets.all(16),
    this.margin = const EdgeInsets.only(bottom: 16),
    this.borderRadius = 12,
    this.width,
    this.height,
  }) : backgroundColor = null,
       border = null,
       boxShadow = null,
       type = AppCardType.upload;

  /// Information display card with subtle background
  const AppCard.info({
    super.key,
    required this.child,
    this.padding = const EdgeInsets.all(16),
    this.margin = const EdgeInsets.only(bottom: 12),
    this.borderRadius = 8,
    this.width,
    this.height,
  }) : backgroundColor = null,
       border = null,
       boxShadow = null,
       type = AppCardType.info;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      margin: margin,
      padding: padding,
      decoration: _getDecoration(),
      child: child,
    );
  }

  BoxDecoration _getDecoration() {
    switch (type) {
      case AppCardType.standard:
        return BoxDecoration(
          color: backgroundColor ?? AppColors.surface,
          borderRadius: BorderRadius.circular(borderRadius),
          border: border,
          boxShadow:
              boxShadow ??
              [
                BoxShadow(
                  color: AppColors.shadow,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
        );

      case AppCardType.section:
        return BoxDecoration(
          color: backgroundColor ?? AppColors.surface,
          borderRadius: BorderRadius.circular(borderRadius),
          border: border ?? Border.all(color: AppColors.border, width: 1),
          boxShadow:
              boxShadow ??
              [
                BoxShadow(
                  color: AppColors.shadow,
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
        );

      case AppCardType.upload:
        return BoxDecoration(
          color: backgroundColor ?? AppColors.surfaceVariant,
          borderRadius: BorderRadius.circular(borderRadius),
          border:
              border ??
              Border.all(
                color: AppColors.border,
                width: 2,
                style: BorderStyle.solid,
              ),
        );

      case AppCardType.info:
        return BoxDecoration(
          color: backgroundColor ?? AppColors.surfaceVariant,
          borderRadius: BorderRadius.circular(borderRadius),
          border: border ?? Border.all(color: AppColors.border, width: 1),
        );
    }
  }
}

/// Different card types for various use cases
enum AppCardType { standard, section, upload, info }
