import 'package:flutter/material.dart';
import 'app_colors.dart';

/// Centralized text styles (Typography system).
/// Based on the professional typography patterns from the provided screen designs.
/// Uses Inter as the primary font for better readability and modern appearance.
class AppTypography {
  static const String primaryFont = "Inter";
  static const String secondaryFont = "Inter";

  // App Bar and Header Styles
  static const TextStyle appBarTitle = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    fontFamily: primaryFont,
    color: AppColors.textOnPrimary,
    letterSpacing: 0.15,
  );

  static const TextStyle screenTitle = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w700,
    fontFamily: primaryFont,
    color: AppColors.textOnPrimary,
    letterSpacing: 0.15,
  );

  // Section Headers
  static const TextStyle sectionHeader = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w600,
    fontFamily: primaryFont,
    color: AppColors.textPrimary,
    letterSpacing: 0.15,
  );

  static const TextStyle sectionSubheader = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    fontFamily: primaryFont,
    color: AppColors.textSecondary,
    letterSpacing: 0.1,
  );

  // Headings
  static const TextStyle h1 = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.w700,
    fontFamily: primaryFont,
    color: AppColors.textPrimary,
    letterSpacing: -0.25,
  );

  static const TextStyle h2 = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w600,
    fontFamily: primaryFont,
    color: AppColors.textPrimary,
    letterSpacing: 0,
  );

  static const TextStyle h3 = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    fontFamily: primaryFont,
    color: AppColors.textPrimary,
    letterSpacing: 0.15,
  );

  static const TextStyle h4 = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w500,
    fontFamily: primaryFont,
    color: AppColors.textPrimary,
    letterSpacing: 0.15,
  );

  // Body Text
  static const TextStyle body1 = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    fontFamily: secondaryFont,
    color: AppColors.textPrimary,
    letterSpacing: 0.5,
    height: 1.5,
  );

  static const TextStyle body2 = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    fontFamily: secondaryFont,
    color: AppColors.textSecondary,
    letterSpacing: 0.25,
    height: 1.43,
  );

  static const TextStyle body3 = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    fontFamily: secondaryFont,
    color: AppColors.textTertiary,
    letterSpacing: 0.4,
    height: 1.33,
  );

  // Labels and Form Fields
  static const TextStyle fieldLabel = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    fontFamily: primaryFont,
    color: AppColors.textPrimary,
    letterSpacing: 0.1,
  );

  static const TextStyle fieldValue = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    fontFamily: secondaryFont,
    color: AppColors.textPrimary,
    letterSpacing: 0.15,
  );

  static const TextStyle fieldHint = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    fontFamily: secondaryFont,
    color: AppColors.textTertiary,
    letterSpacing: 0.15,
  );

  // Buttons
  static const TextStyle buttonPrimary = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    fontFamily: primaryFont,
    color: AppColors.textOnSecondary,
    letterSpacing: 0.5,
  );

  static const TextStyle buttonSecondary = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    fontFamily: primaryFont,
    color: AppColors.primary,
    letterSpacing: 0.5,
  );

  static const TextStyle buttonText = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    fontFamily: primaryFont,
    color: AppColors.primary,
    letterSpacing: 0.25,
  );

  // Legacy styles for backward compatibility
  static const TextStyle label = fieldLabel;
  static const TextStyle button = buttonPrimary;
  static const TextStyle input = fieldValue;
  static const TextStyle hint = fieldHint;

  // Specialized Styles
  static const TextStyle caption = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    fontFamily: secondaryFont,
    color: AppColors.textTertiary,
    letterSpacing: 0.4,
  );

  static const TextStyle overline = TextStyle(
    fontSize: 10,
    fontWeight: FontWeight.w500,
    fontFamily: primaryFont,
    color: AppColors.textTertiary,
    letterSpacing: 1.5,
  );

  static const TextStyle subtitle1 = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    fontFamily: primaryFont,
    color: AppColors.textPrimary,
    letterSpacing: 0.15,
  );

  static const TextStyle subtitle2 = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    fontFamily: primaryFont,
    color: AppColors.textSecondary,
    letterSpacing: 0.1,
  );
}
