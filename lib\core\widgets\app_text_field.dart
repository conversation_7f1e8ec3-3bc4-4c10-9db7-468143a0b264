// App text field widget will be defined here
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/app_colors.dart';
import '../theme/app_typography.dart';

/// Enhanced text field widget matching the screen designs
class AppTextField extends StatefulWidget {
  final TextEditingController controller;
  final String? label;
  final String hintText;
  final String? errorText;
  final bool isValid;
  final bool isRequired;
  final bool obscureText;
  final bool readOnly;
  final void Function(String)? onChanged;
  final VoidCallback? onTap;
  final List<TextInputFormatter>? inputFormatters;
  final TextInputType keyboardType;
  final Widget? prefix;
  final Widget? suffix;
  final FocusNode? focusNode;
  final TextAlign textAlign;
  final int maxLines;
  final int? maxLength;

  // Customizable props
  final double? width;
  final EdgeInsetsGeometry? margin;
  final double borderRadius;
  final TextStyle? textStyle;
  final TextStyle? hintStyle;
  final TextStyle? labelStyle;

  const AppTextField({
    super.key,
    required this.controller,
    required this.hintText,
    this.label,
    this.errorText,
    this.isValid = true,
    this.isRequired = false,
    this.obscureText = false,
    this.readOnly = false,
    this.onChanged,
    this.onTap,
    this.inputFormatters,
    this.keyboardType = TextInputType.text,
    this.prefix,
    this.suffix,
    this.focusNode,
    this.textAlign = TextAlign.start,
    this.maxLines = 1,
    this.maxLength,
    this.width,
    this.margin,
    this.borderRadius = 12,
    this.textStyle,
    this.hintStyle,
    this.labelStyle,
  });

  @override
  State<AppTextField> createState() => _AppTextFieldState();
}

class _AppTextFieldState extends State<AppTextField> {
  late FocusNode _focusNode;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    } else {
      _focusNode.removeListener(_onFocusChange);
    }
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: widget.margin,
      width: widget.width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.label != null) _buildLabel(),
          _buildTextField(),
          if (widget.errorText != null) _buildErrorText(),
        ],
      ),
    );
  }

  Widget _buildLabel() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: RichText(
        text: TextSpan(
          text: widget.label!,
          style: widget.labelStyle ?? AppTypography.fieldLabel,
          children: [
            if (widget.isRequired)
              const TextSpan(
                text: ' *',
                style: TextStyle(color: AppColors.error),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField() {
    return TextFormField(
      controller: widget.controller,
      focusNode: _focusNode,
      keyboardType: widget.keyboardType,
      inputFormatters: widget.inputFormatters,
      onChanged: widget.onChanged,
      onTap: widget.onTap,
      textAlign: widget.textAlign,
      maxLines: widget.maxLines,
      maxLength: widget.maxLength,
      obscureText: widget.obscureText,
      readOnly: widget.readOnly,
      style: widget.textStyle ?? AppTypography.fieldValue,
      decoration: InputDecoration(
        hintText: widget.hintText,
        hintStyle: widget.hintStyle ?? AppTypography.fieldHint,
        prefixIcon: widget.prefix,
        suffixIcon: widget.suffix,
        filled: true,
        fillColor: AppColors.surface,
        border: _buildBorder(AppColors.border),
        enabledBorder: _buildBorder(AppColors.border),
        focusedBorder: _buildBorder(AppColors.borderFocus),
        errorBorder: _buildBorder(AppColors.borderError),
        focusedErrorBorder: _buildBorder(AppColors.borderError),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
        counterText: '', // Hide character counter
      ),
    );
  }

  Widget _buildErrorText() {
    return Padding(
      padding: const EdgeInsets.only(top: 8),
      child: Text(
        widget.errorText!,
        style: AppTypography.caption.copyWith(color: AppColors.error),
      ),
    );
  }

  OutlineInputBorder _buildBorder(Color color) {
    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(widget.borderRadius),
      borderSide: BorderSide(color: color, width: _isFocused ? 2 : 1),
    );
  }
}
