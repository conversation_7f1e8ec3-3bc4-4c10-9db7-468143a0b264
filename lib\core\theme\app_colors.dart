// App colors will be defined here
import 'package:flutter/material.dart';

/// Centralized color definitions for the app.
/// Based on the professional blue and orange theme from the provided screen designs.
class AppColors {
  // Brand Colors - Primary Blue Gradient
  static const Color primary = Color(0xFF1E3A8A); // Deep Blue
  static const Color primaryLight = Color(0xFF3B82F6); // Lighter Blue
  static const Color primaryDark = Color(0xFF1E40AF); // Darker Blue

  // Brand Colors - Secondary Orange Gradient
  static const Color secondary = Color(0xFFFB8F1B); // Primary Orange
  static const Color secondaryLight = Color(0xFFFBBF24); // Light Orange
  static const Color secondaryDark = Color(0xFFEA580C); // Dark Orange

  // Background Colors
  static const Color background = Color(0xFFF8FAFC); // Light gray background
  static const Color backgroundDark = Color(
    0xFF0F172A,
  ); // Dark background for contrast
  static const Color surface = Color(0xFFFFFFFF); // White surface
  static const Color surfaceVariant = Color(
    0xFFF1F5F9,
  ); // Light surface variant

  // Text Colors
  static const Color textPrimary = Color(
    0xFF0F172A,
  ); // Dark text on light backgrounds
  static const Color textSecondary = Color(0xFF475569); // Medium gray text
  static const Color textTertiary = Color(0xFF94A3B8); // Light gray text
  static const Color textOnPrimary =
      Colors.white; // White text on blue backgrounds
  static const Color textOnSecondary =
      Colors.white; // White text on orange backgrounds

  // Border Colors
  static const Color border = Color(0xFFE2E8F0); // Light border
  static const Color borderFocus = Color(
    0xFF3B82F6,
  ); // Blue border for focused elements
  static const Color borderError = Color(0xFFEF4444); // Red border for errors

  // State Colors
  static const Color success = Color(0xFF10B981); // Green success
  static const Color error = Color(0xFFEF4444); // Red error
  static const Color warning = Color(0xFFF59E0B); // Yellow warning
  static const Color info = Color(0xFF3B82F6); // Blue info

  // Additional Utility Colors
  static const Color disabled = Color(0xFFD1D5DB); // Disabled state
  static const Color shadow = Color(0x1A000000); // Subtle shadow
  static const Color overlay = Color(0x80000000); // Dark overlay

  // Gradient Colors for Buttons and Backgrounds
  static const List<Color> primaryGradient = [
    Color(0xFF1E3A8A), // Deep Blue
    Color(0xFF1E40AF), // Darker Blue
  ];

  static const List<Color> secondaryGradient = [
    Color(0xFFFB8F1B), // Primary Orange
    Color(0xFFEA580C), // Dark Orange
  ];
}
